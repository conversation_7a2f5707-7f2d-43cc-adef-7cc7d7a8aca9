'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { NavLink } from '@/components/ui/UnifiedTypography';
import { mainNavItems } from '@/data/navigationLinks';

export default function PerfectNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const pathname = usePathname();

  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY;
    setScrolled(currentScrollY > 20);
  }, []);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Zamknij menu mobilne przy zmianie ścieżki
  useEffect(() => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  const isActiveLink = useCallback((href) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  }, [pathname]);

  const handleDropdownToggle = useCallback((index) => {
    setActiveDropdown(activeDropdown === index ? null : index);
  }, [activeDropdown]);

  const handleMouseEnter = useCallback((index) => {
    if (window.innerWidth >= 1024) { // lg breakpoint
      setActiveDropdown(index);
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (window.innerWidth >= 1024) {
      setActiveDropdown(null);
    }
  }, []);

  return (
    <>
      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ${
        scrolled
          ? 'bg-white/85 backdrop-blur-[20px] shadow-[0_1px_20px_rgba(139,115,85,0.06)] border-b border-enterprise-brown/8'
          : 'bg-transparent backdrop-blur-[20px]'
      }`}
      style={{ height: '80px' }}
      role="navigation"
      aria-label="Główna nawigacja"
      >
        <div className="mx-auto px-6 lg:px-12 xl:px-16">
          <div className="flex items-center justify-between h-20">
            
            {/* Logo - Gradient with glow effect */}
            <Link
              href="/"
              className="group relative font-cormorant font-light transition-all duration-300"
              style={{
                fontSize: '24px',
                letterSpacing: '2px',
                fontWeight: '300',
                background: 'linear-gradient(135deg, #B8935C 0%, #D4AF37 50%, #C9A575 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                filter: 'drop-shadow(0 0 8px rgba(184, 147, 92, 0.3))'
              }}
            >
              BAKASANA
              <span
                className="absolute -bottom-1 left-0 w-0 h-[1px] transition-all duration-300 group-hover:w-full"
                style={{
                  background: 'linear-gradient(90deg, #B8935C, #D4AF37)',
                  boxShadow: '0 0 4px rgba(184, 147, 92, 0.5)'
                }}
              ></span>
            </Link>

            {/* Desktop Menu - Enhanced spacing and animations */}
            <div className="hidden lg:flex items-center space-x-10">
              {mainNavItems.slice(1).map((item, index) => (
                <div 
                  key={item.href} 
                  className="relative group"
                  onMouseEnter={() => handleMouseEnter(index)}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link
                    href={item.href}
                    className="relative group flex items-center space-x-2 py-2"
                  >
                    <NavLink 
                      active={isActiveLink(item.href)}
                      className={`transition-all duration-300 hover:scale-105 ${
                        item.highlight 
                          ? 'text-terra font-medium bg-terra/10 px-3 py-1 rounded-full' 
                          : ''
                      }`}
                    >
                      {item.label}
                    </NavLink>
                    
                    {/* Dropdown arrow */}
                    {item.dropdown && (
                      <svg 
                        className={`w-3 h-3 transition-all duration-300 ${
                          activeDropdown === index ? 'rotate-180 text-enterprise-brown' : 'text-charcoal-light'
                        }`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                      </svg>
                    )}
                    
                    {/* Aktywny wskaźnik - elegancka linia */}
                    {isActiveLink(item.href) && (
                      <span className="absolute -bottom-1 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-enterprise-brown to-transparent opacity-80 rounded-full animate-pulse-gentle"></span>
                    )}
                    
                    {/* Hover efekt - subtelna linia */}
                    <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-[1px] bg-enterprise-brown/60 transition-all duration-500 group-hover:w-3/4"></span>
                  </Link>

                  {/* Enhanced Dropdown Menu */}
                  {item.dropdown && (
                    <div className={`absolute top-full left-1/2 transform -translate-x-1/2 mt-6 
                      bg-white/98 backdrop-blur-xl shadow-premium-shadow border border-enterprise-brown/10
                      transition-all duration-500 min-w-[320px] rounded-2xl overflow-hidden
                      before:absolute before:top-0 before:left-1/2 before:transform before:-translate-x-1/2 before:-translate-y-1 
                      before:w-4 before:h-4 before:bg-white/98 before:rotate-45 before:border-l before:border-t before:border-enterprise-brown/10
                      ${activeDropdown === index 
                        ? 'opacity-100 visible translate-y-0 scale-100' 
                        : 'opacity-0 invisible translate-y-4 scale-95 pointer-events-none'
                      }`}
                    >
                      <div className="py-6">
                        {item.dropdown.map((section, sectionIndex) => (
                          <div key={sectionIndex} className={sectionIndex > 0 ? 'mt-6 pt-6 border-t border-enterprise-brown/10' : ''}>
                            <div className="px-6 mb-3">
                              <h4 className="font-cormorant font-light text-enterprise-brown text-xl tracking-wide flex items-center">
                                <span className="w-2 h-2 bg-enterprise-brown/30 rounded-full mr-3"></span>
                                {section.header}
                              </h4>
                            </div>
                            <div className="space-y-1">
                              {section.items.map((dropdownItem, itemIndex) => (
                                <Link
                                  key={dropdownItem.href}
                                  href={dropdownItem.href}
                                  className="block px-6 py-3 hover:bg-whisper/70 transition-all duration-300 group relative overflow-hidden"
                                  style={{ animationDelay: `${itemIndex * 50}ms` }}
                                >
                                  <div className="absolute left-0 top-0 w-1 h-full bg-enterprise-brown transform scale-y-0 group-hover:scale-y-100 transition-transform duration-300 origin-top"></div>
                                  <NavLink className="text-sm group-hover:text-enterprise-brown group-hover:translate-x-2 transition-all duration-300">
                                    {dropdownItem.label}
                                  </NavLink>
                                </Link>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Book Session Button - Desktop */}
            <div className="hidden lg:block">
              <a
                href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
                target="_blank"
                rel="noopener noreferrer"
                className="group relative px-6 py-2 font-inter font-light text-sm uppercase tracking-[2px] transition-all duration-300"
                style={{
                  background: 'linear-gradient(135deg, #B8935C 0%, #D4AF37 100%)',
                  color: 'white',
                  borderRadius: '2px',
                  boxShadow: '0 4px 12px rgba(184, 147, 92, 0.3)',
                  transform: 'translateY(0)',
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-2px)';
                  e.target.style.boxShadow = '0 8px 20px rgba(184, 147, 92, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = '0 4px 12px rgba(184, 147, 92, 0.3)';
                }}
              >
                BOOK SESSION
                <span
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                    transform: 'translateX(-100%)',
                    animation: 'shimmer 2s infinite'
                  }}
                />
              </a>
            </div>

            {/* Mobile Menu Button - Elegancki hamburger */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-3 text-enterprise-brown hover:text-enterprise-brown/80 transition-all duration-300"
              aria-label="Toggle menu"
            >
              <div className="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 transform ${
                  isMenuOpen ? 'rotate-45 translate-y-[4px]' : ''
                }`}></span>
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 ${
                  isMenuOpen ? 'opacity-0' : ''
                }`}></span>
                <span className={`w-6 h-[1px] bg-current transition-all duration-300 transform ${
                  isMenuOpen ? '-rotate-45 -translate-y-[4px]' : ''
                }`}></span>
              </div>
            </button>
          </div>
        </div>

        {/* Enhanced Mobile Menu */}
        <div className={`lg:hidden absolute top-full left-0 right-0 
          bg-white/98 backdrop-blur-[30px] border-b border-enterprise-brown/10
          transition-all duration-500 ${
          isMenuOpen 
            ? 'opacity-100 translate-y-0 max-h-screen' 
            : 'opacity-0 -translate-y-4 max-h-0 pointer-events-none overflow-hidden'
        }`}>
          <div className="px-6 py-8 space-y-8">
            {mainNavItems.slice(1).map((item, index) => (
              <div 
                key={item.href} 
                className="space-y-3"
                style={{ 
                  animation: isMenuOpen ? `fadeInUp 0.6s ease-out ${index * 100}ms both` : 'none'
                }}
              >
                <div className="flex items-center justify-between group">
                  <Link
                    href={item.href}
                    onClick={() => !item.dropdown && setIsMenuOpen(false)}
                    className="flex-1 relative"
                  >
                    <NavLink 
                      active={isActiveLink(item.href)} 
                      className={`text-lg transition-all duration-300 ${
                        item.highlight 
                          ? 'text-terra font-medium bg-terra/10 px-4 py-2 rounded-full inline-block' 
                          : ''
                      }`}
                    >
                      {item.label}
                      {isActiveLink(item.href) && !item.highlight && (
                        <span className="inline-block ml-3 w-2 h-2 bg-enterprise-brown rounded-full opacity-80 animate-pulse"></span>
                      )}
                    </NavLink>
                    
                    {/* Mobile active indicator */}
                    {isActiveLink(item.href) && !item.highlight && (
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 w-1 h-6 bg-enterprise-brown rounded-full"></div>
                    )}
                  </Link>
                  
                  {/* Mobile dropdown toggle */}
                  {item.dropdown && (
                    <button
                      onClick={() => handleDropdownToggle(index)}
                      className="p-3 text-enterprise-brown/60 hover:text-enterprise-brown hover:bg-whisper/50 rounded-full transition-all duration-300"
                    >
                      <svg 
                        className={`w-5 h-5 transition-transform duration-300 ${
                          activeDropdown === index ? 'rotate-180' : ''
                        }`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  )}
                </div>
                
                {/* Mobile dropdown content */}
                {item.dropdown && (
                  <div className={`pl-6 space-y-4 transition-all duration-500 ${
                    activeDropdown === index 
                      ? 'opacity-100 max-h-96 translate-y-0' 
                      : 'opacity-0 max-h-0 -translate-y-2 overflow-hidden'
                  }`}>
                    {item.dropdown.map((section, sectionIndex) => (
                      <div key={sectionIndex} className="space-y-3">
                        <div className="flex items-center">
                          <div className="w-3 h-[1px] bg-enterprise-brown/30 mr-3"></div>
                          <h5 className="font-cormorant text-enterprise-brown text-base font-light tracking-wide">
                            {section.header}
                          </h5>
                        </div>
                        <div className="space-y-2 pl-6">
                          {section.items.map((dropdownItem, itemIndex) => (
                            <Link
                              key={dropdownItem.href}
                              href={dropdownItem.href}
                              onClick={() => setIsMenuOpen(false)}
                              className="block py-2 group relative"
                              style={{ 
                                animation: activeDropdown === index ? `slideInRight 0.4s ease-out ${itemIndex * 100}ms both` : 'none'
                              }}
                            >
                              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-0 h-[1px] bg-enterprise-brown/50 group-hover:w-4 transition-all duration-300"></div>
                              <NavLink className="text-sm opacity-80 hover:opacity-100 hover:text-enterprise-brown group-hover:translate-x-6 transition-all duration-300">
                                {dropdownItem.label}
                              </NavLink>
                            </Link>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
            
            {/* Book Session Button - Mobile */}
            <div
              className="pt-8 border-t border-enterprise-brown/10"
              style={{
                animation: isMenuOpen ? `fadeInUp 0.6s ease-out ${mainNavItems.length * 100}ms both` : 'none'
              }}
            >
              <a
                href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
                target="_blank"
                rel="noopener noreferrer"
                className="w-full justify-center text-base font-light tracking-[1px] py-4 px-8 rounded-xl hover:scale-105 transition-all duration-300 flex items-center"
                style={{
                  background: 'linear-gradient(135deg, #B8935C 0%, #D4AF37 100%)',
                  color: 'white',
                  boxShadow: '0 4px 12px rgba(184, 147, 92, 0.3)',
                }}
              >
                BOOK SESSION
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Enhanced Mobile Menu Overlay */}
      {isMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/10 backdrop-blur-sm z-40 lg:hidden transition-opacity duration-300"
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      {/* Navbar spacing */}
      <div className="h-20"></div>

      {/* Enhanced Styles */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes slideInRight {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        @keyframes pulseGentle {
          0%, 100% {
            opacity: 0.8;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.02);
          }
        }
        
        .animate-pulse-gentle {
          animation: pulseGentle 2s ease-in-out infinite;
        }
        
        /* Smooth scrollbar for dropdown */
        .dropdown-content::-webkit-scrollbar {
          width: 4px;
        }
        
        .dropdown-content::-webkit-scrollbar-track {
          background: rgba(139, 115, 85, 0.1);
          border-radius: 2px;
        }
        
        .dropdown-content::-webkit-scrollbar-thumb {
          background: rgba(139, 115, 85, 0.3);
          border-radius: 2px;
        }
        
        .dropdown-content::-webkit-scrollbar-thumb:hover {
          background: rgba(139, 115, 85, 0.5);
        }
        
        /* Enhanced hover effects */
        .nav-item-hover {
          position: relative;
          overflow: hidden;
        }
        
        .nav-item-hover::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(139, 115, 85, 0.1), transparent);
          transition: left 0.5s;
        }
        
        .nav-item-hover:hover::before {
          left: 100%;
        }
      `}</style>
    </>
  );
}